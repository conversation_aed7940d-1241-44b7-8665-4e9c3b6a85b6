#!/bin/bash

# ==============================================
# Luminar Platform - Development Start Script
# ==============================================
# Starts all services in development mode with hot reload
# Usage: ./scripts/dev-start.sh [options]
# Options:
#   --no-install    Skip npm/pnpm install
#   --parallel      Start all services in parallel
#   --watch         Enable file watching for all services
# ==============================================

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$( cd "$SCRIPT_DIR/.." && pwd )"

# Load environment
if [ ! -f "$PROJECT_ROOT/.env" ]; then
    echo -e "${YELLOW}Setting up environment...${NC}"
    "$SCRIPT_DIR/setup-env.sh"
fi

source "$PROJECT_ROOT/.env"

# Parse arguments
NO_INSTALL=false
PARALLEL=false
WATCH=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --no-install)
            NO_INSTALL=true
            shift
            ;;
        --parallel)
            PARALLEL=true
            shift
            ;;
        --watch)
            WATCH=true
            shift
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_step() {
    echo -e "${CYAN}→ $1${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

install_dependencies() {
    if [ "$NO_INSTALL" = true ]; then
        return
    fi
    
    print_header "Installing Dependencies"
    
    print_step "Installing root dependencies..."
    pnpm install
    
    print_step "Installing all workspace dependencies..."
    pnpm -r install
    
    print_success "Dependencies installed"
}

start_infrastructure() {
    print_header "Starting Infrastructure (Docker)"
    
    # Start only essential services for development
    docker-compose -f "$PROJECT_ROOT/docker-compose.yml" up -d \
        postgres \
        redis \
        elasticsearch \
        rabbitmq \
        minio \
        qdrant
    
    # Wait for services to be ready
    print_step "Waiting for infrastructure services..."
    sleep 15
    
    print_success "Infrastructure started"
}

setup_database() {
    print_header "Setting Up Database"
    
    cd "$PROJECT_ROOT/apps/command-center"
    
    print_step "Running database migrations..."
    npx prisma migrate dev
    
    print_step "Generating Prisma client..."
    npx prisma generate
    
    print_step "Seeding database with development data..."
    npx prisma db seed || true
    
    cd "$PROJECT_ROOT"
    print_success "Database setup completed"
}

start_development_services() {
    print_header "Starting Development Services"
    
    # Create tmux session for services
    tmux new-session -d -s luminar-dev
    
    # Window 1: Command Center
    tmux rename-window -t luminar-dev:0 'command-center'
    tmux send-keys -t luminar-dev:0 "cd $PROJECT_ROOT/apps/command-center && pnpm run start:dev" C-m
    
    # Window 2: Python Document Processor
    tmux new-window -t luminar-dev:1 -n 'doc-processor'
    tmux send-keys -t luminar-dev:1 "cd $PROJECT_ROOT/apps/python-services && source venv/bin/activate && python main.py" C-m
    
    # Window 3: Frontend Apps
    tmux new-window -t luminar-dev:2 -n 'frontend-apps'
    tmux send-keys -t luminar-dev:2 "cd $PROJECT_ROOT && pnpm run dev:apps" C-m
    
    # Window 4: Packages (if any)
    tmux new-window -t luminar-dev:3 -n 'packages'
    tmux send-keys -t luminar-dev:3 "cd $PROJECT_ROOT && pnpm run dev:packages" C-m
    
    if [ "$WATCH" = true ]; then
        # Window 5: File watchers
        tmux new-window -t luminar-dev:4 -n 'watchers'
        tmux send-keys -t luminar-dev:4 "cd $PROJECT_ROOT && pnpm run test:watch" C-m
    fi
    
    print_success "Development services started in tmux session 'luminar-dev'"
    echo -e "${YELLOW}Tip: Use 'tmux attach -t luminar-dev' to view services${NC}"
}

start_development_tools() {
    print_header "Starting Development Tools"
    
    # Start PgAdmin in background
    if command -v pgadmin4 &> /dev/null; then
        print_step "Starting PgAdmin..."
        nohup pgadmin4 > /dev/null 2>&1 &
    fi
    
    # Start Prisma Studio
    print_step "Starting Prisma Studio..."
    cd "$PROJECT_ROOT/apps/command-center"
    nohup npx prisma studio > /dev/null 2>&1 &
    cd "$PROJECT_ROOT"
    
    print_success "Development tools started"
}

print_dev_info() {
    print_header "Development Environment Ready! 🚀"
    
    echo -e "\n${GREEN}Service URLs:${NC}"
    echo "  • Shell:          http://localhost:$SHELL_DEV_PORT"
    echo "  • AMNA:           http://localhost:$AMNA_DEV_PORT"
    echo "  • E-Connect:      http://localhost:$E_CONNECT_DEV_PORT"
    echo "  • Lighthouse:     http://localhost:$LIGHTHOUSE_DEV_PORT"
    echo "  • Command Center: http://localhost:$COMMAND_CENTER_PORT/api"
    echo "  • Prisma Studio:  http://localhost:$PRISMA_STUDIO_PORT"
    
    echo -e "\n${GREEN}Infrastructure:${NC}"
    echo "  • RabbitMQ:       http://localhost:$RABBITMQ_MGMT_PORT"
    echo "  • MinIO:          http://localhost:$MINIO_CONSOLE_PORT"
    
    echo -e "\n${GREEN}Development Commands:${NC}"
    echo "  • View logs:      tmux attach -t luminar-dev"
    echo "  • Run tests:      pnpm test"
    echo "  • Lint code:      pnpm lint"
    echo "  • Type check:     pnpm typecheck"
    
    echo -e "\n${GREEN}Credentials:${NC}"
    echo "  • Email:          $DEVELOPER_EMAIL"
    echo "  • Password:       $DEVELOPER_PASSWORD"
}

# Main execution
main() {
    print_header "Starting Luminar Development Environment"
    
    # Install dependencies
    install_dependencies
    
    # Start infrastructure
    start_infrastructure
    
    # Setup database
    setup_database
    
    # Start development services
    if [ "$PARALLEL" = true ]; then
        print_step "Starting all services in parallel..."
        cd "$PROJECT_ROOT"
        pnpm run dev &
    else
        start_development_services
    fi
    
    # Start development tools
    start_development_tools
    
    # Print development info
    print_dev_info
}

# Run main function
main
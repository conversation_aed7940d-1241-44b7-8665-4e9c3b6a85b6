# ==============================================
# Luminar L&D Platform - Complete Environment Configuration
# ==============================================
# This file contains ALL available environment variables for the Luminar platform.
# Copy this file to .env and update the values according to your environment.
# For local development, see .env.full-stack.example for minimal required variables.
# For production deployment, see .env.production.example with security guidelines.
# ==============================================

# ==============================================
# DEVELOPER CREDENTIALS (Used across all services)
# ==============================================
# For development: Use these credentials for all services
# For production: Replace with secure, unique credentials per service
DEVELOPER_EMAIL=<EMAIL>
DEVELOPER_PASSWORD=LuminarDev2024!

# ==============================================
# Application Environment
# ==============================================
NODE_ENV=development                    # Options: development, staging, production, test
DATA_PATH=./data                       # Base path for application data storage
APP_VERSION=1.0.0                      # Application version
BUILD_TIME=2024-01-01T00:00:00Z       # Build timestamp
LOG_LEVEL=info                         # Options: error, warn, info, debug, trace
LOG_FORMAT=json                        # Options: json, pretty
TZ=UTC                                 # Timezone

# ==============================================
# Backend Services (3000-3099)
# ==============================================
PORT=3000                              # Main backend service port
COMMAND_CENTER_PORT=3000               # Command Center API port
API_PREFIX=/api/v1                     # API version prefix

# ==============================================
# Frontend Development Ports (5000-5099)
# ==============================================
AMNA_DEV_PORT=5001                     # AMNA AI Assistant port
E_CONNECT_DEV_PORT=5002                # E-Connect Email Assistant port
LIGHTHOUSE_DEV_PORT=5003               # Lighthouse Knowledge Base port
DASHBOARD_DEV_PORT=5004                # Main Dashboard port
TRAINING_DEV_PORT=5005                 # Training Need Analysis port
VENDORS_DEV_PORT=5006                  # Vendor Management port
WINS_DEV_PORT=5007                     # Wins of the Week port
SHELL_DEV_PORT=5008                    # Shell/Launcher port
SERVICE_MONITOR_PORT=5009              # Service Monitor Dashboard port

# ==============================================
# Database Configuration (PostgreSQL)
# ==============================================
DATABASE_HOST=localhost                # Database host
DATABASE_PORT=5432                     # Database port
DATABASE_USERNAME=developer            # Database username (uses developer credentials)
DATABASE_PASSWORD=${DEVELOPER_PASSWORD} # Database password (uses developer password)
DATABASE_NAME=luminar_dev              # Database name
DATABASE_URL=postgresql://${DATABASE_USERNAME}:${DATABASE_PASSWORD}@${DATABASE_HOST}:${DATABASE_PORT}/${DATABASE_NAME}?schema=public
DATABASE_POOL_SIZE=20                  # Connection pool size
DATABASE_TIMEOUT=60000                 # Query timeout in ms
POSTGRES_PORT=5432                     # External PostgreSQL port

# ==============================================
# Redis Configuration
# ==============================================
REDIS_HOST=localhost                   # Redis host
REDIS_PORT=6379                        # Redis port
REDIS_PASSWORD=${DEVELOPER_PASSWORD}   # Redis password (uses developer password)
REDIS_DB=0                            # Redis database number
REDIS_KEY_PREFIX=luminar:             # Redis key prefix
REDIS_TTL=86400                       # Default TTL in seconds (24 hours)

# ==============================================
# Authentication & Security
# ==============================================
JWT_SECRET=luminar-dev-jwt-secret-min-32-characters-long  # JWT signing secret
JWT_EXPIRES_IN=15m                     # JWT token expiration
JWT_REFRESH_EXPIRES_IN=30d             # Refresh token expiration
SESSION_SECRET=luminar-dev-session-secret-min-32-characters-long  # Session secret
BCRYPT_ROUNDS=10                       # Password hashing rounds

# Default admin user (created on first run)
ADMIN_EMAIL=${DEVELOPER_EMAIL}         # Admin email (uses developer email)
ADMIN_PASSWORD=${DEVELOPER_PASSWORD}   # Admin password (uses developer password)

# OAuth Providers
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
MICROSOFT_CLIENT_ID=your-microsoft-client-id
MICROSOFT_CLIENT_SECRET=your-microsoft-client-secret

# Better Auth (for shared-ui/tweakcn)
BETTER_AUTH_SECRET=your-better-auth-secret
BETTER_AUTH_URL=http://localhost:3000

# ==============================================
# AI & Machine Learning Services
# ==============================================
# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-4-turbo-preview      # Model for chat/completion
OPENAI_EMBEDDING_MODEL=text-embedding-3-small  # Model for embeddings
OPENAI_MAX_TOKENS=8191                 # Max tokens per request
OPENAI_TEMPERATURE=0.7                 # Response creativity (0-1)

# Google Gemini Configuration
GEMINI_API_KEY=your-gemini-api-key
GOOGLE_API_KEY=your-google-api-key     # Alternative key name
GEMINI_MODEL=gemini-pro               # Model version

# Groq Configuration
GROQ_API_KEY=your-groq-api-key
GROQ_MODEL=mixtral-8x7b-32768

# Embeddings Configuration
EMBEDDINGS_PROVIDER=openai             # Options: openai, huggingface, local
EMBEDDINGS_DIMENSIONS=1536             # Vector dimensions
EMBEDDINGS_BATCH_SIZE=100              # Batch processing size

# ==============================================
# Vector Database (Qdrant)
# ==============================================
QDRANT_HOST=localhost                  # Qdrant host
QDRANT_PORT=6333                       # Qdrant REST API port
QDRANT_GRPC_PORT=6334                  # Qdrant gRPC port
QDRANT_API_KEY=                        # Qdrant API key (if secured)
QDRANT_TIMEOUT=30000                   # Request timeout in ms
QDRANT_COLLECTION=luminar_vectors      # Default collection name

# ==============================================
# Local AI (Ollama)
# ==============================================
OLLAMA_HOST=localhost                  # Ollama host
OLLAMA_PORT=11434                      # Ollama port
OLLAMA_MODEL=llama2                    # Default model
OLLAMA_EMBEDDING_MODEL=nomic-embed-text # Embedding model

# ==============================================
# Search & Analytics (Elasticsearch)
# ==============================================
ELASTICSEARCH_HOST=localhost
ELASTICSEARCH_PORT=9200
ELASTICSEARCH_TRANSPORT_PORT=9300
ELASTICSEARCH_USERNAME=elastic         # Elasticsearch username
ELASTICSEARCH_PASSWORD=${DEVELOPER_PASSWORD}  # Elasticsearch password (uses developer password)
ELASTICSEARCH_INDEX_PREFIX=luminar_
ELASTICSEARCH_CLUSTER_NAME=luminar-cluster
ELASTICSEARCH_NODE_NAME=luminar-node

# ==============================================
# Message Queue (RabbitMQ)
# ==============================================
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_MGMT_PORT=15672
RABBITMQ_USERNAME=developer            # RabbitMQ username (uses developer credentials)
RABBITMQ_PASSWORD=${DEVELOPER_PASSWORD} # RabbitMQ password (uses developer password)
RABBITMQ_VHOST=/
RABBITMQ_EXCHANGE=luminar_exchange
RABBITMQ_QUEUE_PREFIX=luminar_

# ==============================================
# File Storage Configuration
# ==============================================
FILE_STORAGE_PROVIDER=local            # Options: local, minio, s3
MAX_FILE_SIZE=104857600               # 100MB in bytes
ALLOWED_MIME_TYPES=image/jpeg,image/png,application/pdf,video/mp4  # Comma-separated

# Local Storage Configuration
LOCAL_UPLOAD_PATH=./uploads
LOCAL_PUBLIC_PATH=./public
LOCAL_THUMBNAIL_PATH=./uploads/thumbnails
LOCAL_TEMP_PATH=./uploads/temp

# MinIO/S3 Configuration
MINIO_HOST=localhost
MINIO_PORT=9000
MINIO_CONSOLE_PORT=9001
MINIO_USE_SSL=false
MINIO_ACCESS_KEY=${DEVELOPER_EMAIL}    # MinIO access key (uses developer email)
MINIO_SECRET_KEY=${DEVELOPER_PASSWORD}  # MinIO secret key (uses developer password)
MINIO_ROOT_USER=${DEVELOPER_EMAIL}     # Alternative key name (uses developer email)
MINIO_ROOT_PASSWORD=${DEVELOPER_PASSWORD}  # Alternative key name (uses developer password)
MINIO_BUCKET=files
MINIO_REGION=us-east-1
MINIO_DEFAULT_BUCKETS=uploads,documents,backups,training-assets,vendor-files,wins-attachments

# AWS S3 Configuration (Production)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
S3_BUCKET=luminar-production
S3_ENDPOINT=                          # Custom endpoint for S3-compatible services

# ==============================================
# File Processing Configuration
# ==============================================
ENABLE_IMAGE_PROCESSING=true
ENABLE_VIDEO_PROCESSING=true
ENABLE_DOCUMENT_PROCESSING=true
ENABLE_AUDIO_PROCESSING=true
ENABLE_VIRUS_SCANNING=true
ENABLE_OCR=true
ENABLE_TEXT_EXTRACTION=true
ENABLE_AUDIO_TRANSCRIPTION=false

# Image Processing
IMAGE_QUALITY=85                       # JPEG quality (1-100)
IMAGE_FORMAT=jpeg                      # Default output format
STRIP_IMAGE_METADATA=true              # Remove EXIF data

# Video Processing
VIDEO_THUMBNAIL_COUNT=3                # Number of thumbnails to generate
VIDEO_THUMBNAIL_INTERVAL=10            # Seconds between thumbnails
MAX_VIDEO_DURATION=3600               # Max duration in seconds (1 hour)
VIDEO_COMPRESSION_LEVEL=medium         # Options: low, medium, high
VIDEO_OUTPUT_FORMATS=mp4              # Comma-separated formats

# Document Processing
PDF_COMPRESSION_LEVEL=medium           # Options: low, medium, high

# Audio Processing
AUDIO_OUTPUT_FORMATS=mp3              # Comma-separated formats
AUDIO_BITRATE=128k                    # Audio bitrate

# ==============================================
# Python Services (8000-8099)
# ==============================================
DOCUMENT_PROCESSOR_PORT=8001
DOCUMENT_PROCESSOR_HOST=localhost
LUMINAR_DOC_PROCESSING_PORT=8001      # Alternative key name
LUMINAR_DOC_REDIS_HOST=localhost
LUMINAR_DOC_REDIS_PORT=6379
LUMINAR_DOC_DATABASE_URL=${DATABASE_URL}
LUMINAR_DOC_LOG_LEVEL=INFO

# ==============================================
# Email Configuration
# ==============================================
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USER=${DEVELOPER_EMAIL}           # Email username (uses developer email)
MAIL_PASSWORD=${DEVELOPER_PASSWORD}     # Email password (uses developer password)
MAIL_FROM=${DEVELOPER_EMAIL}           # From address (uses developer email)
MAIL_TLS=true

# ==============================================
# Frontend Configuration
# ==============================================
VITE_API_URL=http://localhost:3000
VITE_API_BASE_URL=http://localhost:3000/api/v1
VITE_WS_URL=ws://localhost:3000
VITE_PUBLIC_URL=http://localhost:5000
VITE_APP_NAME=Luminar L&D Platform
VITE_APP_VERSION=${APP_VERSION}
VITE_BUILD_TIME=${BUILD_TIME}
VITE_SENTRY_DSN=                      # Frontend Sentry DSN
VITE_GA_TRACKING_ID=                  # Google Analytics ID
VITE_ENABLE_PWA=true                  # Progressive Web App features

# ==============================================
# Monitoring & Observability
# ==============================================
# Sentry Error Tracking
SENTRY_DSN=your-sentry-dsn
SENTRY_ENVIRONMENT=${NODE_ENV}
SENTRY_RELEASE=${APP_VERSION}
SENTRY_TRACES_SAMPLE_RATE=0.1         # 10% of transactions
SENTRY_PROFILES_SAMPLE_RATE=0.1       # 10% profiling

# Prometheus Metrics
PROMETHEUS_PORT=9090
METRICS_ENABLED=true
METRICS_PATH=/metrics

# Grafana
GRAFANA_PORT=3000
GRAFANA_USER=developer                 # Grafana username (uses developer credentials)
GRAFANA_PASSWORD=${DEVELOPER_PASSWORD}  # Grafana password (uses developer password)

# Jaeger Tracing
JAEGER_PORT=16686
JAEGER_AGENT_HOST=localhost
JAEGER_AGENT_PORT=6831
TRACING_ENABLED=true

# Logging
LOKI_URL=http://localhost:3100
FLUENT_BIT_PORT=24224

# ==============================================
# Security & Rate Limiting
# ==============================================
RATE_LIMIT_WINDOW=900000              # 15 minutes in ms
RATE_LIMIT_MAX=100                   # Max requests per window
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=false
RATE_LIMIT_SKIP_FAILED_REQUESTS=false

# CORS Configuration
CORS_ORIGINS=http://localhost:5001,http://localhost:5002,http://localhost:5003,http://localhost:5004,http://localhost:5005,http://localhost:5006,http://localhost:5007,http://localhost:5008
CORS_CREDENTIALS=true
CORS_MAX_AGE=86400                    # 24 hours

# Security Headers
HELMET_ENABLED=true
CSP_ENABLED=true
HSTS_ENABLED=true

# ==============================================
# Feature Flags
# ==============================================
ENABLE_MONITORING=true
ENABLE_TRACING=true
ENABLE_CACHING=true
ENABLE_ANALYTICS=true
ENABLE_NOTIFICATIONS=true
ENABLE_WEBSOCKETS=true
ENABLE_FILE_UPLOADS=true
ENABLE_AI_FEATURES=true
ENABLE_MULTI_TENANT=false
ENABLE_API_VERSIONING=true
ENABLE_SWAGGER_UI=true
ENABLE_GRAPHQL=false

# ==============================================
# Development Tools
# ==============================================
PGADMIN_EMAIL=${DEVELOPER_EMAIL}       # PgAdmin email (uses developer email)
PGADMIN_PASSWORD=${DEVELOPER_PASSWORD}  # PgAdmin password (uses developer password)
PGADMIN_PORT=5050

PRISMA_STUDIO_PORT=5555
PRISMA_LOG_LEVEL=info                 # Options: info, query, warn, error

DEV_MONITOR_PORT=9089                 # Development monitoring dashboard
STORYBOOK_PORT=6006                   # Storybook UI components
STORYBOOK=false                       # Storybook mode flag

# ==============================================
# Docker & Infrastructure
# ==============================================
COMPOSE_PROJECT_NAME=luminar
DOCKER_REGISTRY=docker.io
DOCKER_NAMESPACE=luminar
DOCKER_TAG=latest

# Kubernetes
K8S_NAMESPACE=luminar
K8S_INGRESS_HOST=luminar.local
K8S_INGRESS_TLS_ENABLED=true

# Health Check
HEALTH_CHECK_INTERVAL=30000           # 30 seconds
HEALTH_CHECK_TIMEOUT=5000             # 5 seconds
HEALTH_CHECK_RETRIES=3

# ==============================================
# Third-party Integrations
# ==============================================
# Stripe Payment Processing
STRIPE_PUBLIC_KEY=pk_test_xxx
STRIPE_SECRET_KEY=sk_test_xxx
STRIPE_WEBHOOK_SECRET=whsec_xxx

# Twilio SMS/Voice
TWILIO_ACCOUNT_SID=your-account-sid
TWILIO_AUTH_TOKEN=your-auth-token
TWILIO_PHONE_NUMBER=+**********

# SendGrid Email
SENDGRID_API_KEY=your-sendgrid-key

# Slack Integration
SLACK_APP_TOKEN=xapp-xxx
SLACK_BOT_TOKEN=xoxb-xxx
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/xxx

# ==============================================
# Miscellaneous
# ==============================================
# Timezone and Locale
DEFAULT_TIMEZONE=UTC
DEFAULT_LOCALE=en-US
SUPPORTED_LOCALES=en-US,es-ES,fr-FR,de-DE,zh-CN,ja-JP

# API Keys for external services
WEATHER_API_KEY=                      # If weather widget is used
MAPS_API_KEY=                         # Google Maps API key
TRANSLATION_API_KEY=                  # Translation service key

# Analytics and Tracking
MIXPANEL_TOKEN=                       # Mixpanel analytics
AMPLITUDE_API_KEY=                    # Amplitude analytics
HOTJAR_ID=                           # Hotjar tracking

# CDN Configuration
CDN_URL=                             # CDN base URL
CDN_ENABLED=false                    # Enable CDN for assets
/*
  Warnings:

  - You are about to drop the column `checksum` on the `AuditLog` table. All the data in the column will be lost.
  - The `changes` column on the `AuditLog` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `metadata` column on the `AuditLog` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `severity` column on the `AuditLog` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `email` on the `EmailVerificationToken` table. All the data in the column will be lost.
  - You are about to drop the column `ipAddress` on the `EmailVerificationToken` table. All the data in the column will be lost.
  - You are about to drop the column `verifiedAt` on the `EmailVerificationToken` table. All the data in the column will be lost.
  - You are about to drop the column `created_at` on the `FeatureConfig` table. All the data in the column will be lost.
  - You are about to drop the column `enabled` on the `FeatureConfig` table. All the data in the column will be lost.
  - You are about to drop the column `name` on the `FeatureConfig` table. All the data in the column will be lost.
  - You are about to drop the column `updated_at` on the `FeatureConfig` table. All the data in the column will be lost.
  - You are about to drop the column `description` on the `FeatureFlag` table. All the data in the column will be lost.
  - You are about to drop the column `name` on the `FeatureFlag` table. All the data in the column will be lost.
  - You are about to drop the column `rules` on the `FeatureFlag` table. All the data in the column will be lost.
  - You are about to drop the column `ipAddress` on the `PasswordResetToken` table. All the data in the column will be lost.
  - You are about to drop the column `userAgent` on the `PasswordResetToken` table. All the data in the column will be lost.
  - You are about to drop the column `lastRun` on the `Report` table. All the data in the column will be lost.
  - You are about to drop the column `nextRun` on the `Report` table. All the data in the column will be lost.
  - You are about to drop the column `results` on the `Report` table. All the data in the column will be lost.
  - You are about to drop the column `schedule` on the `Report` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `Report` table. All the data in the column will be lost.
  - The `createdBy` column on the `Report` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `deviceId` on the `UserSession` table. All the data in the column will be lost.
  - You are about to drop the column `revokedAt` on the `UserSession` table. All the data in the column will be lost.
  - You are about to drop the column `revokedReason` on the `UserSession` table. All the data in the column will be lost.
  - You are about to drop the column `token` on the `UserSession` table. All the data in the column will be lost.
  - You are about to drop the `ApiKey` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Assessment` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `AssessmentResponse` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `BudgetAllocation` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `CompetencyFramework` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Department` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `LearningActivity` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `LearningPath` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Notification` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `PrivacyRequest` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Proposal` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Review` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Role` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Skill` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `SkillGap` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `TrainingCourse` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `TrainingEnrollment` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `TrainingRecommendation` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `User` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `UserConsent` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `UserSkill` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Vendor` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `WeeklySubmission` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Workflow` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `WorkflowExecution` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `_CourseSkills` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `_PathCourses` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `_PathSkills` table. If the table is not empty, all the data it contains will be lost.
  - A unique constraint covering the columns `[feature]` on the table `FeatureFlag` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[sessionToken]` on the table `UserSession` will be added. If there are existing duplicate values, this will fail.
  - Made the column `entityType` on table `AuditLog` required. This step will fail if there are existing NULL values in that column.
  - Added the required column `updatedAt` to the `FeatureConfig` table without a default value. This is not possible if the table is not empty.
  - Added the required column `feature` to the `FeatureFlag` table without a default value. This is not possible if the table is not empty.
  - Made the column `resource` on table `Permission` required. This step will fail if there are existing NULL values in that column.
  - Made the column `action` on table `Permission` required. This step will fail if there are existing NULL values in that column.
  - Added the required column `sessionToken` to the `UserSession` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updatedAt` to the `UserSession` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "prisma"."AuditEventType" AS ENUM ('LOGIN', 'LOGOUT', 'LOGIN_FAILED', 'ACCOUNT_LOCKED', 'DATA_ACCESS', 'DATA_VIEW', 'DATA_CREATE', 'DATA_UPDATE', 'DATA_DELETE', 'SECURITY_VIOLATION', 'PERMISSION_DENIED', 'UNAUTHORIZED_ACCESS', 'SUSPICIOUS_ACTIVITY', 'SECURITY_SCAN', 'CONSENT_GIVEN', 'CONSENT_WITHDRAWN', 'DATA_RETENTION_EXECUTED', 'DATA_ANONYMIZED', 'COMPLIANCE_REPORT_GENERATED', 'SYSTEM_EVENT', 'CONFIGURATION_CHANGE', 'API_ACCESS', 'HTTP_REQUEST', 'FILE_ACCESS', 'EXPORT', 'IMPORT');

-- CreateEnum
CREATE TYPE "prisma"."AuditSeverity" AS ENUM ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL');

-- CreateEnum
CREATE TYPE "prisma"."AuditStatus" AS ENUM ('SUCCESS', 'FAILURE', 'PENDING', 'CANCELLED');

-- CreateEnum
CREATE TYPE "prisma"."ConversationType" AS ENUM ('CHAT', 'COMPLETION', 'AGENT');

-- CreateEnum
CREATE TYPE "prisma"."ConversationStatus" AS ENUM ('ACTIVE', 'ARCHIVED', 'DELETED');

-- CreateEnum
CREATE TYPE "prisma"."MessageRole" AS ENUM ('USER', 'ASSISTANT', 'SYSTEM');

-- CreateEnum
CREATE TYPE "prisma"."MessageStatus" AS ENUM ('PENDING', 'SENT', 'STREAMING', 'COMPLETED', 'FAILED');

-- CreateEnum
CREATE TYPE "prisma"."MemoryType" AS ENUM ('SHORT_TERM', 'LONG_TERM', 'EPISODIC', 'SEMANTIC');

-- CreateEnum
CREATE TYPE "prisma"."MemoryStatus" AS ENUM ('ACTIVE', 'CONSOLIDATED', 'EXPIRED', 'ARCHIVED');

-- CreateEnum
CREATE TYPE "prisma"."TemplateCategory" AS ENUM ('CUSTOM', 'CHAT', 'CODE_GENERATION', 'SUMMARIZATION', 'QUESTION_ANSWERING', 'ANALYSIS');

-- CreateEnum
CREATE TYPE "prisma"."TemplateStatus" AS ENUM ('ACTIVE', 'INACTIVE', 'DEPRECATED');

-- CreateEnum
CREATE TYPE "prisma"."SessionStatus" AS ENUM ('ACTIVE', 'TERMINATED', 'EXPIRED');

-- CreateEnum
CREATE TYPE "prisma"."EmailProvider" AS ENUM ('GMAIL', 'OUTLOOK', 'EXCHANGE', 'IMAP', 'POP3');

-- CreateEnum
CREATE TYPE "prisma"."SyncType" AS ENUM ('FULL', 'INCREMENTAL', 'MANUAL', 'SCHEDULED');

-- CreateEnum
CREATE TYPE "prisma"."SyncStatus" AS ENUM ('PENDING', 'RUNNING', 'SUCCESS', 'COMPLETED', 'FAILED', 'FAILED_RETRYABLE', 'PARTIAL', 'CANCELLED');

-- CreateEnum
CREATE TYPE "prisma"."AccountStatus" AS ENUM ('ACTIVE', 'INACTIVE', 'SUSPENDED', 'REAUTH_REQUIRED', 'ERROR');

-- CreateEnum
CREATE TYPE "prisma"."FileType" AS ENUM ('DOCUMENT', 'IMAGE', 'VIDEO', 'AUDIO', 'ARCHIVE', 'SPREADSHEET', 'PRESENTATION', 'CODE', 'OTHER');

-- CreateEnum
CREATE TYPE "prisma"."FileStatus" AS ENUM ('UPLOADING', 'UPLOADED', 'PROCESSING', 'PROCESSED', 'ERROR', 'DELETED', 'QUARANTINED', 'FAILED', 'READY');

-- CreateEnum
CREATE TYPE "prisma"."ConsentType" AS ENUM ('MARKETING', 'ANALYTICS', 'FUNCTIONAL', 'NECESSARY', 'PERSONALIZATION', 'ADVERTISING', 'THIRD_PARTY');

-- CreateEnum
CREATE TYPE "prisma"."ConsentStatus" AS ENUM ('GIVEN', 'WITHDRAWN', 'EXPIRED', 'PENDING');

-- CreateEnum
CREATE TYPE "prisma"."LegalBasis" AS ENUM ('CONSENT', 'CONTRACT', 'LEGAL_OBLIGATION', 'VITAL_INTERESTS', 'PUBLIC_TASK', 'LEGITIMATE_INTERESTS');

-- CreateEnum
CREATE TYPE "prisma"."DataRequestType" AS ENUM ('ACCESS', 'DELETION', 'PORTABILITY', 'RECTIFICATION', 'ERASURE', 'RESTRICT_PROCESSING', 'OBJECTION');

-- CreateEnum
CREATE TYPE "prisma"."DataRequestStatus" AS ENUM ('PENDING', 'IN_PROGRESS', 'PROCESSING', 'COMPLETED', 'DENIED', 'REJECTED', 'CANCELLED');

-- CreateEnum
CREATE TYPE "prisma"."DataCategory" AS ENUM ('PERSONAL_DATA', 'SENSITIVE_DATA', 'BEHAVIORAL_DATA', 'TECHNICAL_DATA', 'COMMUNICATION_DATA');

-- CreateEnum
CREATE TYPE "prisma"."RetentionAction" AS ENUM ('DELETE', 'ANONYMIZE', 'ARCHIVE', 'REVIEW');

-- DropForeignKey
ALTER TABLE "prisma"."Assessment" DROP CONSTRAINT "Assessment_departmentId_fkey";

-- DropForeignKey
ALTER TABLE "prisma"."Assessment" DROP CONSTRAINT "Assessment_roleId_fkey";

-- DropForeignKey
ALTER TABLE "prisma"."AssessmentResponse" DROP CONSTRAINT "AssessmentResponse_assessmentId_fkey";

-- DropForeignKey
ALTER TABLE "prisma"."AssessmentResponse" DROP CONSTRAINT "AssessmentResponse_userId_fkey";

-- DropForeignKey
ALTER TABLE "prisma"."AuthAttempt" DROP CONSTRAINT "AuthAttempt_userId_fkey";

-- DropForeignKey
ALTER TABLE "prisma"."BudgetAllocation" DROP CONSTRAINT "BudgetAllocation_departmentId_fkey";

-- DropForeignKey
ALTER TABLE "prisma"."EmailVerificationToken" DROP CONSTRAINT "EmailVerificationToken_userId_fkey";

-- DropForeignKey
ALTER TABLE "prisma"."LearningActivity" DROP CONSTRAINT "LearningActivity_userId_fkey";

-- DropForeignKey
ALTER TABLE "prisma"."LearningPath" DROP CONSTRAINT "LearningPath_roleId_fkey";

-- DropForeignKey
ALTER TABLE "prisma"."PasswordResetToken" DROP CONSTRAINT "PasswordResetToken_userId_fkey";

-- DropForeignKey
ALTER TABLE "prisma"."PrivacyRequest" DROP CONSTRAINT "PrivacyRequest_userId_fkey";

-- DropForeignKey
ALTER TABLE "prisma"."Proposal" DROP CONSTRAINT "Proposal_vendorId_fkey";

-- DropForeignKey
ALTER TABLE "prisma"."RefreshToken" DROP CONSTRAINT "RefreshToken_userId_fkey";

-- DropForeignKey
ALTER TABLE "prisma"."Review" DROP CONSTRAINT "Review_proposalId_fkey";

-- DropForeignKey
ALTER TABLE "prisma"."Review" DROP CONSTRAINT "Review_vendorId_fkey";

-- DropForeignKey
ALTER TABLE "prisma"."Role" DROP CONSTRAINT "Role_departmentId_fkey";

-- DropForeignKey
ALTER TABLE "prisma"."SkillGap" DROP CONSTRAINT "SkillGapUser";

-- DropForeignKey
ALTER TABLE "prisma"."SkillGap" DROP CONSTRAINT "SkillGap_skillId_fkey";

-- DropForeignKey
ALTER TABLE "prisma"."TrainingEnrollment" DROP CONSTRAINT "TrainingEnrollment_courseId_fkey";

-- DropForeignKey
ALTER TABLE "prisma"."TrainingEnrollment" DROP CONSTRAINT "TrainingEnrollment_userId_fkey";

-- DropForeignKey
ALTER TABLE "prisma"."TrainingRecommendation" DROP CONSTRAINT "TrainingRecommendation_courseId_fkey";

-- DropForeignKey
ALTER TABLE "prisma"."TrainingRecommendation" DROP CONSTRAINT "TrainingRecommendation_skillGapId_fkey";

-- DropForeignKey
ALTER TABLE "prisma"."TrainingRecommendation" DROP CONSTRAINT "TrainingRecommendation_userId_fkey";

-- DropForeignKey
ALTER TABLE "prisma"."User" DROP CONSTRAINT "User_departmentId_fkey";

-- DropForeignKey
ALTER TABLE "prisma"."User" DROP CONSTRAINT "User_managerId_fkey";

-- DropForeignKey
ALTER TABLE "prisma"."User" DROP CONSTRAINT "User_roleId_fkey";

-- DropForeignKey
ALTER TABLE "prisma"."UserConsent" DROP CONSTRAINT "UserConsent_userId_fkey";

-- DropForeignKey
ALTER TABLE "prisma"."UserSession" DROP CONSTRAINT "UserSession_userId_fkey";

-- DropForeignKey
ALTER TABLE "prisma"."UserSkill" DROP CONSTRAINT "UserSkill_skillId_fkey";

-- DropForeignKey
ALTER TABLE "prisma"."UserSkill" DROP CONSTRAINT "UserSkill_userId_fkey";

-- DropForeignKey
ALTER TABLE "prisma"."WeeklySubmission" DROP CONSTRAINT "WeeklySubmission_userId_fkey";

-- DropForeignKey
ALTER TABLE "prisma"."WorkflowExecution" DROP CONSTRAINT "WorkflowExecution_workflowId_fkey";

-- DropForeignKey
ALTER TABLE "prisma"."_CourseSkills" DROP CONSTRAINT "_CourseSkills_A_fkey";

-- DropForeignKey
ALTER TABLE "prisma"."_CourseSkills" DROP CONSTRAINT "_CourseSkills_B_fkey";

-- DropForeignKey
ALTER TABLE "prisma"."_PathCourses" DROP CONSTRAINT "_PathCourses_A_fkey";

-- DropForeignKey
ALTER TABLE "prisma"."_PathCourses" DROP CONSTRAINT "_PathCourses_B_fkey";

-- DropForeignKey
ALTER TABLE "prisma"."_PathSkills" DROP CONSTRAINT "_PathSkills_A_fkey";

-- DropForeignKey
ALTER TABLE "prisma"."_PathSkills" DROP CONSTRAINT "_PathSkills_B_fkey";

-- DropIndex
DROP INDEX "prisma"."AuditLog_performedBy_idx";

-- DropIndex
DROP INDEX "prisma"."AuthAttempt_type_result_idx";

-- DropIndex
DROP INDEX "prisma"."AuthAttempt_userId_idx";

-- DropIndex
DROP INDEX "prisma"."FeatureConfig_enabled_idx";

-- DropIndex
DROP INDEX "prisma"."FeatureConfig_feature_key_idx";

-- DropIndex
DROP INDEX "prisma"."FeatureFlag_name_key";

-- DropIndex
DROP INDEX "prisma"."Permission_resource_action_idx";

-- DropIndex
DROP INDEX "prisma"."Report_type_status_idx";

-- AlterTable
ALTER TABLE "prisma"."AuditLog" DROP COLUMN "checksum",
ADD COLUMN     "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ALTER COLUMN "entityType" SET NOT NULL,
DROP COLUMN "changes",
ADD COLUMN     "changes" JSONB,
DROP COLUMN "metadata",
ADD COLUMN     "metadata" JSONB,
ALTER COLUMN "performedBy" DROP NOT NULL,
DROP COLUMN "severity",
ADD COLUMN     "severity" "prisma"."AuditSeverity" NOT NULL DEFAULT 'LOW';

-- AlterTable
ALTER TABLE "prisma"."AuthAttempt" ADD COLUMN     "details" JSONB,
ALTER COLUMN "email" DROP NOT NULL;

-- AlterTable
ALTER TABLE "prisma"."EmailVerificationToken" DROP COLUMN "email",
DROP COLUMN "ipAddress",
DROP COLUMN "verifiedAt";

-- AlterTable
ALTER TABLE "prisma"."FeatureConfig" DROP COLUMN "created_at",
DROP COLUMN "enabled",
DROP COLUMN "name",
DROP COLUMN "updated_at",
ADD COLUMN     "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "updatedAt" TIMESTAMP(3) NOT NULL;

-- AlterTable
ALTER TABLE "prisma"."FeatureFlag" DROP COLUMN "description",
DROP COLUMN "name",
DROP COLUMN "rules",
ADD COLUMN     "conditions" JSONB,
ADD COLUMN     "feature" TEXT NOT NULL,
ADD COLUMN     "rollout" DOUBLE PRECISION NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE "prisma"."PasswordResetToken" DROP COLUMN "ipAddress",
DROP COLUMN "userAgent";

-- AlterTable
ALTER TABLE "prisma"."Permission" ADD COLUMN     "conditions" JSONB,
ADD COLUMN     "isActive" BOOLEAN NOT NULL DEFAULT true,
ALTER COLUMN "resource" SET NOT NULL,
ALTER COLUMN "action" SET NOT NULL;

-- AlterTable
ALTER TABLE "prisma"."Report" DROP COLUMN "lastRun",
DROP COLUMN "nextRun",
DROP COLUMN "results",
DROP COLUMN "schedule",
DROP COLUMN "updatedAt",
ADD COLUMN     "completedAt" TIMESTAMP(3),
ADD COLUMN     "error" TEXT,
ADD COLUMN     "fileUrl" TEXT,
ADD COLUMN     "format" TEXT NOT NULL DEFAULT 'pdf',
ALTER COLUMN "status" SET DEFAULT 'pending',
DROP COLUMN "createdBy",
ADD COLUMN     "createdBy" INTEGER;

-- AlterTable
ALTER TABLE "prisma"."UserSession" DROP COLUMN "deviceId",
DROP COLUMN "revokedAt",
DROP COLUMN "revokedReason",
DROP COLUMN "token",
ADD COLUMN     "deviceInfo" JSONB,
ADD COLUMN     "lastActivityAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "metadata" JSONB,
ADD COLUMN     "sessionToken" TEXT NOT NULL,
ADD COLUMN     "terminatedAt" TIMESTAMP(3),
ADD COLUMN     "updatedAt" TIMESTAMP(3) NOT NULL;

-- DropTable
DROP TABLE "prisma"."ApiKey";

-- DropTable
DROP TABLE "prisma"."Assessment";

-- DropTable
DROP TABLE "prisma"."AssessmentResponse";

-- DropTable
DROP TABLE "prisma"."BudgetAllocation";

-- DropTable
DROP TABLE "prisma"."CompetencyFramework";

-- DropTable
DROP TABLE "prisma"."Department";

-- DropTable
DROP TABLE "prisma"."LearningActivity";

-- DropTable
DROP TABLE "prisma"."LearningPath";

-- DropTable
DROP TABLE "prisma"."Notification";

-- DropTable
DROP TABLE "prisma"."PrivacyRequest";

-- DropTable
DROP TABLE "prisma"."Proposal";

-- DropTable
DROP TABLE "prisma"."Review";

-- DropTable
DROP TABLE "prisma"."Role";

-- DropTable
DROP TABLE "prisma"."Skill";

-- DropTable
DROP TABLE "prisma"."SkillGap";

-- DropTable
DROP TABLE "prisma"."TrainingCourse";

-- DropTable
DROP TABLE "prisma"."TrainingEnrollment";

-- DropTable
DROP TABLE "prisma"."TrainingRecommendation";

-- DropTable
DROP TABLE "prisma"."User";

-- DropTable
DROP TABLE "prisma"."UserConsent";

-- DropTable
DROP TABLE "prisma"."UserSkill";

-- DropTable
DROP TABLE "prisma"."Vendor";

-- DropTable
DROP TABLE "prisma"."WeeklySubmission";

-- DropTable
DROP TABLE "prisma"."Workflow";

-- DropTable
DROP TABLE "prisma"."WorkflowExecution";

-- DropTable
DROP TABLE "prisma"."_CourseSkills";

-- DropTable
DROP TABLE "prisma"."_PathCourses";

-- DropTable
DROP TABLE "prisma"."_PathSkills";

-- CreateTable
CREATE TABLE "prisma"."ErrorLog" (
    "id" TEXT NOT NULL,
    "error_type" TEXT NOT NULL,
    "error_message" TEXT NOT NULL,
    "error_stack" TEXT,
    "context" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ErrorLog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."User" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "firstName" TEXT,
    "lastName" TEXT,
    "role" TEXT NOT NULL DEFAULT 'user',
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "isEmailVerified" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "lastLoginAt" TIMESTAMP(3),
    "refreshToken" TEXT,

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Department" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "description" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "managerId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Department_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."UserDepartment" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "departmentId" TEXT NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "endDate" TIMESTAMP(3),
    "isPrimary" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "UserDepartment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Role" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "permissions" JSONB NOT NULL DEFAULT '[]',
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Role_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."UserRole" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "roleId" TEXT NOT NULL,
    "assignedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "assignedBy" TEXT,

    CONSTRAINT "UserRole_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Skill" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "departmentId" TEXT,
    "description" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Skill_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."UserSkill" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "skillId" TEXT NOT NULL,
    "proficiencyLevel" TEXT NOT NULL,
    "verifiedAt" TIMESTAMP(3),
    "verifiedBy" TEXT,
    "expiresAt" TIMESTAMP(3),

    CONSTRAINT "UserSkill_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Training" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "category" TEXT NOT NULL,
    "departmentId" TEXT,
    "trainerId" TEXT,
    "duration" INTEGER,
    "format" TEXT NOT NULL,
    "maxParticipants" INTEGER,
    "status" TEXT NOT NULL DEFAULT 'draft',
    "startDate" TIMESTAMP(3),
    "endDate" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Training_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."TrainingSkill" (
    "id" TEXT NOT NULL,
    "trainingId" TEXT NOT NULL,
    "skillId" TEXT NOT NULL,
    "isRequired" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "TrainingSkill_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."TrainingUser" (
    "id" TEXT NOT NULL,
    "trainingId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'enrolled',
    "enrolledAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "startedAt" TIMESTAMP(3),
    "completedAt" TIMESTAMP(3),
    "score" DOUBLE PRECISION,
    "certificateUrl" TEXT,
    "feedback" TEXT,

    CONSTRAINT "TrainingUser_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."TrainingMaterial" (
    "id" TEXT NOT NULL,
    "trainingId" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "url" TEXT,
    "content" TEXT,
    "order" INTEGER NOT NULL DEFAULT 0,
    "isRequired" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TrainingMaterial_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Vendor" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "companyName" TEXT,
    "code" TEXT NOT NULL,
    "description" TEXT,
    "category" TEXT,
    "email" TEXT,
    "phoneNumbers" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "website" TEXT,
    "contactName" TEXT,
    "contactEmail" TEXT,
    "contactPhone" TEXT,
    "address" TEXT,
    "status" TEXT NOT NULL DEFAULT 'active',
    "rating" DOUBLE PRECISION,
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Vendor_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."VendorCategory" (
    "id" TEXT NOT NULL,
    "vendorId" TEXT NOT NULL,
    "category" TEXT NOT NULL,

    CONSTRAINT "VendorCategory_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."VendorContract" (
    "id" TEXT NOT NULL,
    "vendorId" TEXT NOT NULL,
    "contractNumber" TEXT NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "value" DOUBLE PRECISION,
    "currency" TEXT NOT NULL DEFAULT 'USD',
    "status" TEXT NOT NULL DEFAULT 'active',
    "documentUrl" TEXT,
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "VendorContract_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Proposal" (
    "id" TEXT NOT NULL,
    "vendorId" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "value" DOUBLE PRECISION,
    "currency" TEXT NOT NULL DEFAULT 'USD',
    "status" TEXT NOT NULL DEFAULT 'pending',
    "submittedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Proposal_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Review" (
    "id" TEXT NOT NULL,
    "vendorId" TEXT NOT NULL,
    "rating" DOUBLE PRECISION NOT NULL,
    "comment" TEXT,
    "reviewedBy" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Review_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."SystemMetric" (
    "id" TEXT NOT NULL,
    "metric" TEXT NOT NULL,
    "value" DOUBLE PRECISION NOT NULL,
    "tags" JSONB NOT NULL DEFAULT '{}',
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "SystemMetric_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."WeeklySubmission" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "weekStartDate" TIMESTAMP(3) NOT NULL,
    "weekEndDate" TIMESTAMP(3) NOT NULL,
    "accomplishments" TEXT NOT NULL,
    "challenges" TEXT,
    "nextWeekPlans" TEXT,
    "additionalNotes" TEXT,
    "achievements" JSONB DEFAULT '[]',
    "costInitiatives" JSONB DEFAULT '[]',
    "trainingIdeas" JSONB DEFAULT '[]',
    "progressUpdates" JSONB DEFAULT '[]',
    "status" TEXT NOT NULL DEFAULT 'draft',
    "submittedAt" TIMESTAMP(3),
    "reviewedAt" TIMESTAMP(3),
    "reviewedBy" INTEGER,
    "reviewNotes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "WeeklySubmission_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."AiConversation" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "type" "prisma"."ConversationType" NOT NULL DEFAULT 'CHAT',
    "status" "prisma"."ConversationStatus" NOT NULL DEFAULT 'ACTIVE',
    "model" TEXT NOT NULL DEFAULT 'gpt-3.5-turbo',
    "systemPromptId" TEXT,
    "modelConfig" JSONB,
    "memoryConfig" JSONB,
    "contextWindow" INTEGER NOT NULL DEFAULT 4096,
    "tags" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "metadata" JSONB,
    "lastMessageAt" TIMESTAMP(3),
    "archivedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AiConversation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."AiMessage" (
    "id" TEXT NOT NULL,
    "conversationId" TEXT NOT NULL,
    "role" "prisma"."MessageRole" NOT NULL,
    "content" TEXT NOT NULL,
    "attachments" JSONB,
    "metadata" JSONB,
    "status" "prisma"."MessageStatus" NOT NULL DEFAULT 'SENT',
    "tokens" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AiMessage_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."AiMemory" (
    "id" TEXT NOT NULL,
    "conversationId" TEXT,
    "userId" TEXT,
    "key" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "type" "prisma"."MemoryType" NOT NULL DEFAULT 'SHORT_TERM',
    "status" "prisma"."MemoryStatus" NOT NULL DEFAULT 'ACTIVE',
    "importance" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "accessCount" INTEGER NOT NULL DEFAULT 0,
    "lastAccessedAt" TIMESTAMP(3),
    "consolidatedAt" TIMESTAMP(3),
    "expiresAt" TIMESTAMP(3),
    "metadata" JSONB,
    "vector" DOUBLE PRECISION[] DEFAULT ARRAY[]::DOUBLE PRECISION[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AiMemory_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."PromptTemplate" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "content" TEXT NOT NULL,
    "variables" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "category" "prisma"."TemplateCategory" NOT NULL DEFAULT 'CUSTOM',
    "status" "prisma"."TemplateStatus" NOT NULL DEFAULT 'ACTIVE',
    "version" TEXT NOT NULL DEFAULT '1.0.0',
    "tags" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "usage_count" INTEGER NOT NULL DEFAULT 0,
    "metadata" JSONB,
    "createdBy" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PromptTemplate_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."Document" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "content" TEXT,
    "description" TEXT,
    "type" TEXT NOT NULL,
    "mimeType" TEXT,
    "ownerId" TEXT NOT NULL,
    "folderId" TEXT,
    "tags" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "metadata" JSONB,
    "isPublic" BOOLEAN NOT NULL DEFAULT false,
    "viewCount" INTEGER NOT NULL DEFAULT 0,
    "deletedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Document_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."DocumentChunk" (
    "id" TEXT NOT NULL,
    "documentId" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "index" INTEGER NOT NULL,
    "chunkIndex" INTEGER NOT NULL,
    "startChar" INTEGER,
    "endChar" INTEGER,
    "tokens" INTEGER,
    "vector" DOUBLE PRECISION[] DEFAULT ARRAY[]::DOUBLE PRECISION[],
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "DocumentChunk_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."Folder" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "path" TEXT NOT NULL,
    "parentId" TEXT,
    "ownerId" TEXT NOT NULL,
    "isPublic" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Folder_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."Tag" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "color" TEXT,
    "description" TEXT,
    "ownerId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Tag_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."UserIntegrationPreferences" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "platform" TEXT NOT NULL,
    "preferences" JSONB NOT NULL DEFAULT '{}',
    "isEnabled" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "UserIntegrationPreferences_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."IntegrationLogs" (
    "id" TEXT NOT NULL,
    "platform" TEXT NOT NULL,
    "operation" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "details" JSONB,
    "error" TEXT,
    "processingTime" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "IntegrationLogs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."ConsentRecord" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "consentType" "prisma"."ConsentType" NOT NULL,
    "status" "prisma"."ConsentStatus" NOT NULL DEFAULT 'PENDING',
    "purpose" TEXT NOT NULL,
    "legalBasis" "prisma"."LegalBasis",
    "source" TEXT,
    "givenAt" TIMESTAMP(3) NOT NULL,
    "withdrawnAt" TIMESTAMP(3),
    "expiresAt" TIMESTAMP(3),
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ConsentRecord_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."DataRequest" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "type" "prisma"."DataRequestType" NOT NULL,
    "status" "prisma"."DataRequestStatus" NOT NULL DEFAULT 'PENDING',
    "details" JSONB,
    "response" JSONB,
    "requestedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "DataRequest_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."DataProcessingLog" (
    "id" TEXT NOT NULL,
    "operation" TEXT NOT NULL,
    "entityType" TEXT NOT NULL,
    "entityId" TEXT,
    "userId" TEXT,
    "purpose" TEXT NOT NULL,
    "legalBasis" TEXT,
    "dataTypes" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "details" JSONB,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "DataProcessingLog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."DataRetentionPolicy" (
    "id" TEXT NOT NULL,
    "entityType" TEXT NOT NULL,
    "category" "prisma"."DataCategory",
    "retentionPeriod" INTEGER NOT NULL,
    "retentionPeriodDays" INTEGER NOT NULL,
    "action" "prisma"."RetentionAction" NOT NULL DEFAULT 'DELETE',
    "description" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "DataRetentionPolicy_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."PersonalDataInventory" (
    "id" TEXT NOT NULL,
    "dataType" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "description" TEXT,
    "location" TEXT NOT NULL,
    "retention" INTEGER,
    "isProcessing" BOOLEAN NOT NULL DEFAULT true,
    "legalBasis" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PersonalDataInventory_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."ComplianceReport" (
    "id" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "parameters" JSONB,
    "results" JSONB,
    "generatedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ComplianceReport_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."ErrorGroup" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "fingerprint" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'open',
    "firstSeen" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "lastSeen" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "eventCount" INTEGER NOT NULL DEFAULT 0,
    "userCount" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ErrorGroup_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."ErrorEvent" (
    "id" TEXT NOT NULL,
    "groupId" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "stack" TEXT,
    "level" TEXT NOT NULL DEFAULT 'error',
    "platform" TEXT,
    "release" TEXT,
    "environment" TEXT,
    "userId" TEXT,
    "context" JSONB,
    "tags" JSONB,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ErrorEvent_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."ErrorResolution" (
    "id" TEXT NOT NULL,
    "groupId" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "resolution" TEXT,
    "resolvedBy" TEXT,
    "resolvedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ErrorResolution_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."ActivityLog" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "activity" TEXT NOT NULL,
    "details" JSONB,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ActivityLog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."Alert" (
    "id" TEXT NOT NULL,
    "ruleId" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'active',
    "severity" TEXT NOT NULL DEFAULT 'medium',
    "title" TEXT NOT NULL,
    "description" TEXT,
    "metadata" JSONB,
    "triggeredAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "resolvedAt" TIMESTAMP(3),

    CONSTRAINT "Alert_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."AlertRule" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "condition" JSONB NOT NULL,
    "threshold" JSONB,
    "isEnabled" BOOLEAN NOT NULL DEFAULT true,
    "channels" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AlertRule_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."EmailAccount" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "displayName" TEXT,
    "provider" "prisma"."EmailProvider" NOT NULL DEFAULT 'GMAIL',
    "accessToken" TEXT,
    "refreshToken" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "status" "prisma"."AccountStatus" NOT NULL DEFAULT 'ACTIVE',
    "credentials" JSONB,
    "settings" JSONB,
    "lastSyncAt" TIMESTAMP(3),
    "lastSuccessfulSyncAt" TIMESTAMP(3),
    "syncStats" JSONB,
    "lastSyncError" TEXT,
    "consecutiveFailures" INTEGER DEFAULT 0,
    "syncEnabled" BOOLEAN DEFAULT true,
    "disabledReason" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "EmailAccount_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."EmailSyncLog" (
    "id" TEXT NOT NULL,
    "emailAccountId" TEXT NOT NULL,
    "accountId" TEXT NOT NULL,
    "syncType" "prisma"."SyncType" NOT NULL DEFAULT 'MANUAL',
    "operation" TEXT NOT NULL,
    "status" "prisma"."SyncStatus" NOT NULL DEFAULT 'PENDING',
    "messageCount" INTEGER,
    "emailsProcessed" INTEGER DEFAULT 0,
    "emailsCreated" INTEGER DEFAULT 0,
    "emailsUpdated" INTEGER DEFAULT 0,
    "emailsSkipped" INTEGER DEFAULT 0,
    "error" TEXT,
    "errorMessage" TEXT,
    "attemptCount" INTEGER DEFAULT 1,
    "errorDetails" JSONB,
    "metadata" JSONB,
    "performance" JSONB,
    "startedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "EmailSyncLog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."File" (
    "id" TEXT NOT NULL,
    "filename" TEXT NOT NULL,
    "originalName" TEXT NOT NULL,
    "extension" TEXT,
    "mimeType" TEXT NOT NULL,
    "size" INTEGER NOT NULL,
    "path" TEXT NOT NULL,
    "publicUrl" TEXT,
    "storagePath" TEXT,
    "checksum" TEXT,
    "ownerId" TEXT NOT NULL,
    "uploadedBy" TEXT,
    "folderId" TEXT,
    "isPublic" BOOLEAN NOT NULL DEFAULT false,
    "isTemporary" BOOLEAN NOT NULL DEFAULT false,
    "tags" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "type" "prisma"."FileType",
    "status" "prisma"."FileStatus" NOT NULL DEFAULT 'UPLOADED',
    "viewCount" INTEGER DEFAULT 0,
    "downloadCount" INTEGER DEFAULT 0,
    "variants" JSONB,
    "processingData" JSONB,
    "metadata" JSONB,
    "thumbnailUrl" TEXT,
    "lastAccessedAt" TIMESTAMP(3),
    "deletedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "File_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."FileVersion" (
    "id" TEXT NOT NULL,
    "fileId" TEXT NOT NULL,
    "version" INTEGER NOT NULL,
    "size" INTEGER NOT NULL,
    "path" TEXT NOT NULL,
    "checksum" TEXT,
    "createdBy" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "FileVersion_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."FeatureUsage" (
    "id" TEXT NOT NULL,
    "userId" TEXT,
    "feature" TEXT NOT NULL,
    "action" TEXT NOT NULL,
    "count" INTEGER NOT NULL DEFAULT 1,
    "metadata" JSONB,
    "date" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "FeatureUsage_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."FeatureMetrics" (
    "id" TEXT NOT NULL,
    "feature" TEXT NOT NULL,
    "metric" TEXT NOT NULL,
    "value" DOUBLE PRECISION NOT NULL,
    "metadata" JSONB,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "FeatureMetrics_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."FeatureAdoption" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "feature" TEXT NOT NULL,
    "adoptedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "metadata" JSONB,

    CONSTRAINT "FeatureAdoption_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."UserEvent" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "event" TEXT NOT NULL,
    "properties" JSONB,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "UserEvent_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."Dashboard" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "layout" JSONB,
    "isPublic" BOOLEAN NOT NULL DEFAULT false,
    "ownerId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Dashboard_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."DashboardWidget" (
    "id" TEXT NOT NULL,
    "dashboardId" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "title" TEXT,
    "configuration" JSONB NOT NULL,
    "position" JSONB NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "DashboardWidget_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."NotificationChannel" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "configuration" JSONB NOT NULL,
    "isEnabled" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "NotificationChannel_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ErrorLog_error_type_idx" ON "prisma"."ErrorLog"("error_type");

-- CreateIndex
CREATE INDEX "ErrorLog_createdAt_idx" ON "prisma"."ErrorLog"("createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "User_email_key" ON "public"."User"("email");

-- CreateIndex
CREATE INDEX "User_email_idx" ON "public"."User"("email");

-- CreateIndex
CREATE INDEX "User_role_idx" ON "public"."User"("role");

-- CreateIndex
CREATE UNIQUE INDEX "Department_name_key" ON "public"."Department"("name");

-- CreateIndex
CREATE UNIQUE INDEX "Department_code_key" ON "public"."Department"("code");

-- CreateIndex
CREATE INDEX "Department_code_idx" ON "public"."Department"("code");

-- CreateIndex
CREATE INDEX "UserDepartment_userId_idx" ON "public"."UserDepartment"("userId");

-- CreateIndex
CREATE INDEX "UserDepartment_departmentId_idx" ON "public"."UserDepartment"("departmentId");

-- CreateIndex
CREATE UNIQUE INDEX "UserDepartment_userId_departmentId_key" ON "public"."UserDepartment"("userId", "departmentId");

-- CreateIndex
CREATE UNIQUE INDEX "Role_name_key" ON "public"."Role"("name");

-- CreateIndex
CREATE INDEX "Role_name_idx" ON "public"."Role"("name");

-- CreateIndex
CREATE INDEX "UserRole_userId_idx" ON "public"."UserRole"("userId");

-- CreateIndex
CREATE INDEX "UserRole_roleId_idx" ON "public"."UserRole"("roleId");

-- CreateIndex
CREATE UNIQUE INDEX "UserRole_userId_roleId_key" ON "public"."UserRole"("userId", "roleId");

-- CreateIndex
CREATE UNIQUE INDEX "Skill_name_key" ON "public"."Skill"("name");

-- CreateIndex
CREATE INDEX "Skill_category_idx" ON "public"."Skill"("category");

-- CreateIndex
CREATE INDEX "Skill_departmentId_idx" ON "public"."Skill"("departmentId");

-- CreateIndex
CREATE INDEX "UserSkill_userId_idx" ON "public"."UserSkill"("userId");

-- CreateIndex
CREATE INDEX "UserSkill_skillId_idx" ON "public"."UserSkill"("skillId");

-- CreateIndex
CREATE INDEX "UserSkill_proficiencyLevel_idx" ON "public"."UserSkill"("proficiencyLevel");

-- CreateIndex
CREATE UNIQUE INDEX "UserSkill_userId_skillId_key" ON "public"."UserSkill"("userId", "skillId");

-- CreateIndex
CREATE INDEX "Training_category_idx" ON "public"."Training"("category");

-- CreateIndex
CREATE INDEX "Training_status_idx" ON "public"."Training"("status");

-- CreateIndex
CREATE INDEX "Training_departmentId_idx" ON "public"."Training"("departmentId");

-- CreateIndex
CREATE INDEX "Training_startDate_endDate_idx" ON "public"."Training"("startDate", "endDate");

-- CreateIndex
CREATE INDEX "TrainingSkill_trainingId_idx" ON "public"."TrainingSkill"("trainingId");

-- CreateIndex
CREATE INDEX "TrainingSkill_skillId_idx" ON "public"."TrainingSkill"("skillId");

-- CreateIndex
CREATE UNIQUE INDEX "TrainingSkill_trainingId_skillId_key" ON "public"."TrainingSkill"("trainingId", "skillId");

-- CreateIndex
CREATE INDEX "TrainingUser_trainingId_idx" ON "public"."TrainingUser"("trainingId");

-- CreateIndex
CREATE INDEX "TrainingUser_userId_idx" ON "public"."TrainingUser"("userId");

-- CreateIndex
CREATE INDEX "TrainingUser_status_idx" ON "public"."TrainingUser"("status");

-- CreateIndex
CREATE UNIQUE INDEX "TrainingUser_trainingId_userId_key" ON "public"."TrainingUser"("trainingId", "userId");

-- CreateIndex
CREATE INDEX "TrainingMaterial_trainingId_idx" ON "public"."TrainingMaterial"("trainingId");

-- CreateIndex
CREATE INDEX "TrainingMaterial_type_idx" ON "public"."TrainingMaterial"("type");

-- CreateIndex
CREATE UNIQUE INDEX "Vendor_name_key" ON "public"."Vendor"("name");

-- CreateIndex
CREATE UNIQUE INDEX "Vendor_code_key" ON "public"."Vendor"("code");

-- CreateIndex
CREATE INDEX "Vendor_code_idx" ON "public"."Vendor"("code");

-- CreateIndex
CREATE INDEX "Vendor_status_idx" ON "public"."Vendor"("status");

-- CreateIndex
CREATE INDEX "VendorCategory_vendorId_idx" ON "public"."VendorCategory"("vendorId");

-- CreateIndex
CREATE INDEX "VendorCategory_category_idx" ON "public"."VendorCategory"("category");

-- CreateIndex
CREATE UNIQUE INDEX "VendorCategory_vendorId_category_key" ON "public"."VendorCategory"("vendorId", "category");

-- CreateIndex
CREATE UNIQUE INDEX "VendorContract_contractNumber_key" ON "public"."VendorContract"("contractNumber");

-- CreateIndex
CREATE INDEX "VendorContract_vendorId_idx" ON "public"."VendorContract"("vendorId");

-- CreateIndex
CREATE INDEX "VendorContract_status_idx" ON "public"."VendorContract"("status");

-- CreateIndex
CREATE INDEX "VendorContract_startDate_endDate_idx" ON "public"."VendorContract"("startDate", "endDate");

-- CreateIndex
CREATE INDEX "Proposal_vendorId_idx" ON "public"."Proposal"("vendorId");

-- CreateIndex
CREATE INDEX "Proposal_status_idx" ON "public"."Proposal"("status");

-- CreateIndex
CREATE INDEX "Review_vendorId_idx" ON "public"."Review"("vendorId");

-- CreateIndex
CREATE INDEX "Review_rating_idx" ON "public"."Review"("rating");

-- CreateIndex
CREATE INDEX "SystemMetric_metric_timestamp_idx" ON "prisma"."SystemMetric"("metric", "timestamp");

-- CreateIndex
CREATE INDEX "SystemMetric_timestamp_idx" ON "prisma"."SystemMetric"("timestamp");

-- CreateIndex
CREATE INDEX "WeeklySubmission_userId_idx" ON "public"."WeeklySubmission"("userId");

-- CreateIndex
CREATE INDEX "WeeklySubmission_weekStartDate_idx" ON "public"."WeeklySubmission"("weekStartDate");

-- CreateIndex
CREATE INDEX "WeeklySubmission_status_idx" ON "public"."WeeklySubmission"("status");

-- CreateIndex
CREATE UNIQUE INDEX "WeeklySubmission_userId_weekStartDate_key" ON "public"."WeeklySubmission"("userId", "weekStartDate");

-- CreateIndex
CREATE INDEX "AiConversation_userId_idx" ON "prisma"."AiConversation"("userId");

-- CreateIndex
CREATE INDEX "AiConversation_status_idx" ON "prisma"."AiConversation"("status");

-- CreateIndex
CREATE INDEX "AiConversation_type_idx" ON "prisma"."AiConversation"("type");

-- CreateIndex
CREATE INDEX "AiConversation_lastMessageAt_idx" ON "prisma"."AiConversation"("lastMessageAt");

-- CreateIndex
CREATE INDEX "AiMessage_conversationId_idx" ON "prisma"."AiMessage"("conversationId");

-- CreateIndex
CREATE INDEX "AiMessage_role_idx" ON "prisma"."AiMessage"("role");

-- CreateIndex
CREATE INDEX "AiMessage_status_idx" ON "prisma"."AiMessage"("status");

-- CreateIndex
CREATE INDEX "AiMessage_createdAt_idx" ON "prisma"."AiMessage"("createdAt");

-- CreateIndex
CREATE INDEX "AiMemory_conversationId_idx" ON "prisma"."AiMemory"("conversationId");

-- CreateIndex
CREATE INDEX "AiMemory_userId_idx" ON "prisma"."AiMemory"("userId");

-- CreateIndex
CREATE INDEX "AiMemory_type_idx" ON "prisma"."AiMemory"("type");

-- CreateIndex
CREATE INDEX "AiMemory_status_idx" ON "prisma"."AiMemory"("status");

-- CreateIndex
CREATE INDEX "AiMemory_key_idx" ON "prisma"."AiMemory"("key");

-- CreateIndex
CREATE INDEX "AiMemory_importance_idx" ON "prisma"."AiMemory"("importance");

-- CreateIndex
CREATE UNIQUE INDEX "PromptTemplate_name_key" ON "prisma"."PromptTemplate"("name");

-- CreateIndex
CREATE INDEX "PromptTemplate_category_idx" ON "prisma"."PromptTemplate"("category");

-- CreateIndex
CREATE INDEX "PromptTemplate_status_idx" ON "prisma"."PromptTemplate"("status");

-- CreateIndex
CREATE INDEX "PromptTemplate_name_idx" ON "prisma"."PromptTemplate"("name");

-- CreateIndex
CREATE INDEX "Document_ownerId_idx" ON "prisma"."Document"("ownerId");

-- CreateIndex
CREATE INDEX "Document_folderId_idx" ON "prisma"."Document"("folderId");

-- CreateIndex
CREATE INDEX "Document_type_idx" ON "prisma"."Document"("type");

-- CreateIndex
CREATE INDEX "DocumentChunk_documentId_idx" ON "prisma"."DocumentChunk"("documentId");

-- CreateIndex
CREATE INDEX "DocumentChunk_index_idx" ON "prisma"."DocumentChunk"("index");

-- CreateIndex
CREATE INDEX "DocumentChunk_chunkIndex_idx" ON "prisma"."DocumentChunk"("chunkIndex");

-- CreateIndex
CREATE INDEX "Folder_ownerId_idx" ON "prisma"."Folder"("ownerId");

-- CreateIndex
CREATE INDEX "Folder_parentId_idx" ON "prisma"."Folder"("parentId");

-- CreateIndex
CREATE INDEX "Folder_path_idx" ON "prisma"."Folder"("path");

-- CreateIndex
CREATE UNIQUE INDEX "Tag_name_key" ON "prisma"."Tag"("name");

-- CreateIndex
CREATE INDEX "Tag_name_idx" ON "prisma"."Tag"("name");

-- CreateIndex
CREATE INDEX "Tag_ownerId_idx" ON "prisma"."Tag"("ownerId");

-- CreateIndex
CREATE INDEX "UserIntegrationPreferences_userId_idx" ON "prisma"."UserIntegrationPreferences"("userId");

-- CreateIndex
CREATE INDEX "UserIntegrationPreferences_platform_idx" ON "prisma"."UserIntegrationPreferences"("platform");

-- CreateIndex
CREATE UNIQUE INDEX "UserIntegrationPreferences_userId_platform_key" ON "prisma"."UserIntegrationPreferences"("userId", "platform");

-- CreateIndex
CREATE INDEX "IntegrationLogs_platform_idx" ON "prisma"."IntegrationLogs"("platform");

-- CreateIndex
CREATE INDEX "IntegrationLogs_status_idx" ON "prisma"."IntegrationLogs"("status");

-- CreateIndex
CREATE INDEX "IntegrationLogs_createdAt_idx" ON "prisma"."IntegrationLogs"("createdAt");

-- CreateIndex
CREATE INDEX "ConsentRecord_userId_idx" ON "prisma"."ConsentRecord"("userId");

-- CreateIndex
CREATE INDEX "ConsentRecord_consentType_idx" ON "prisma"."ConsentRecord"("consentType");

-- CreateIndex
CREATE INDEX "ConsentRecord_status_idx" ON "prisma"."ConsentRecord"("status");

-- CreateIndex
CREATE INDEX "DataRequest_userId_idx" ON "prisma"."DataRequest"("userId");

-- CreateIndex
CREATE INDEX "DataRequest_type_idx" ON "prisma"."DataRequest"("type");

-- CreateIndex
CREATE INDEX "DataRequest_status_idx" ON "prisma"."DataRequest"("status");

-- CreateIndex
CREATE INDEX "DataProcessingLog_operation_idx" ON "prisma"."DataProcessingLog"("operation");

-- CreateIndex
CREATE INDEX "DataProcessingLog_entityType_idx" ON "prisma"."DataProcessingLog"("entityType");

-- CreateIndex
CREATE INDEX "DataProcessingLog_userId_idx" ON "prisma"."DataProcessingLog"("userId");

-- CreateIndex
CREATE INDEX "DataProcessingLog_timestamp_idx" ON "prisma"."DataProcessingLog"("timestamp");

-- CreateIndex
CREATE INDEX "DataRetentionPolicy_entityType_idx" ON "prisma"."DataRetentionPolicy"("entityType");

-- CreateIndex
CREATE INDEX "DataRetentionPolicy_category_idx" ON "prisma"."DataRetentionPolicy"("category");

-- CreateIndex
CREATE INDEX "DataRetentionPolicy_action_idx" ON "prisma"."DataRetentionPolicy"("action");

-- CreateIndex
CREATE INDEX "DataRetentionPolicy_isActive_idx" ON "prisma"."DataRetentionPolicy"("isActive");

-- CreateIndex
CREATE INDEX "PersonalDataInventory_dataType_idx" ON "prisma"."PersonalDataInventory"("dataType");

-- CreateIndex
CREATE INDEX "PersonalDataInventory_category_idx" ON "prisma"."PersonalDataInventory"("category");

-- CreateIndex
CREATE INDEX "PersonalDataInventory_isProcessing_idx" ON "prisma"."PersonalDataInventory"("isProcessing");

-- CreateIndex
CREATE INDEX "ComplianceReport_type_idx" ON "prisma"."ComplianceReport"("type");

-- CreateIndex
CREATE INDEX "ComplianceReport_status_idx" ON "prisma"."ComplianceReport"("status");

-- CreateIndex
CREATE INDEX "ComplianceReport_generatedAt_idx" ON "prisma"."ComplianceReport"("generatedAt");

-- CreateIndex
CREATE UNIQUE INDEX "ErrorGroup_fingerprint_key" ON "prisma"."ErrorGroup"("fingerprint");

-- CreateIndex
CREATE INDEX "ErrorGroup_status_idx" ON "prisma"."ErrorGroup"("status");

-- CreateIndex
CREATE INDEX "ErrorGroup_type_idx" ON "prisma"."ErrorGroup"("type");

-- CreateIndex
CREATE INDEX "ErrorGroup_lastSeen_idx" ON "prisma"."ErrorGroup"("lastSeen");

-- CreateIndex
CREATE INDEX "ErrorEvent_groupId_idx" ON "prisma"."ErrorEvent"("groupId");

-- CreateIndex
CREATE INDEX "ErrorEvent_level_idx" ON "prisma"."ErrorEvent"("level");

-- CreateIndex
CREATE INDEX "ErrorEvent_timestamp_idx" ON "prisma"."ErrorEvent"("timestamp");

-- CreateIndex
CREATE INDEX "ErrorEvent_userId_idx" ON "prisma"."ErrorEvent"("userId");

-- CreateIndex
CREATE INDEX "ErrorResolution_groupId_idx" ON "prisma"."ErrorResolution"("groupId");

-- CreateIndex
CREATE INDEX "ErrorResolution_status_idx" ON "prisma"."ErrorResolution"("status");

-- CreateIndex
CREATE INDEX "ActivityLog_userId_idx" ON "prisma"."ActivityLog"("userId");

-- CreateIndex
CREATE INDEX "ActivityLog_activity_idx" ON "prisma"."ActivityLog"("activity");

-- CreateIndex
CREATE INDEX "ActivityLog_timestamp_idx" ON "prisma"."ActivityLog"("timestamp");

-- CreateIndex
CREATE INDEX "Alert_ruleId_idx" ON "prisma"."Alert"("ruleId");

-- CreateIndex
CREATE INDEX "Alert_status_idx" ON "prisma"."Alert"("status");

-- CreateIndex
CREATE INDEX "Alert_severity_idx" ON "prisma"."Alert"("severity");

-- CreateIndex
CREATE INDEX "Alert_triggeredAt_idx" ON "prisma"."Alert"("triggeredAt");

-- CreateIndex
CREATE INDEX "AlertRule_name_idx" ON "prisma"."AlertRule"("name");

-- CreateIndex
CREATE INDEX "AlertRule_isEnabled_idx" ON "prisma"."AlertRule"("isEnabled");

-- CreateIndex
CREATE INDEX "EmailAccount_userId_idx" ON "prisma"."EmailAccount"("userId");

-- CreateIndex
CREATE INDEX "EmailAccount_provider_idx" ON "prisma"."EmailAccount"("provider");

-- CreateIndex
CREATE INDEX "EmailAccount_status_idx" ON "prisma"."EmailAccount"("status");

-- CreateIndex
CREATE UNIQUE INDEX "EmailAccount_userId_email_key" ON "prisma"."EmailAccount"("userId", "email");

-- CreateIndex
CREATE INDEX "EmailSyncLog_accountId_idx" ON "prisma"."EmailSyncLog"("accountId");

-- CreateIndex
CREATE INDEX "EmailSyncLog_emailAccountId_idx" ON "prisma"."EmailSyncLog"("emailAccountId");

-- CreateIndex
CREATE INDEX "EmailSyncLog_status_idx" ON "prisma"."EmailSyncLog"("status");

-- CreateIndex
CREATE INDEX "EmailSyncLog_syncType_idx" ON "prisma"."EmailSyncLog"("syncType");

-- CreateIndex
CREATE INDEX "EmailSyncLog_startedAt_idx" ON "prisma"."EmailSyncLog"("startedAt");

-- CreateIndex
CREATE INDEX "File_ownerId_idx" ON "prisma"."File"("ownerId");

-- CreateIndex
CREATE INDEX "File_uploadedBy_idx" ON "prisma"."File"("uploadedBy");

-- CreateIndex
CREATE INDEX "File_filename_idx" ON "prisma"."File"("filename");

-- CreateIndex
CREATE INDEX "File_mimeType_idx" ON "prisma"."File"("mimeType");

-- CreateIndex
CREATE INDEX "File_type_idx" ON "prisma"."File"("type");

-- CreateIndex
CREATE INDEX "File_status_idx" ON "prisma"."File"("status");

-- CreateIndex
CREATE INDEX "FileVersion_fileId_idx" ON "prisma"."FileVersion"("fileId");

-- CreateIndex
CREATE INDEX "FileVersion_createdBy_idx" ON "prisma"."FileVersion"("createdBy");

-- CreateIndex
CREATE UNIQUE INDEX "FileVersion_fileId_version_key" ON "prisma"."FileVersion"("fileId", "version");

-- CreateIndex
CREATE INDEX "FeatureUsage_feature_idx" ON "prisma"."FeatureUsage"("feature");

-- CreateIndex
CREATE INDEX "FeatureUsage_action_idx" ON "prisma"."FeatureUsage"("action");

-- CreateIndex
CREATE INDEX "FeatureUsage_date_idx" ON "prisma"."FeatureUsage"("date");

-- CreateIndex
CREATE UNIQUE INDEX "FeatureUsage_userId_feature_action_date_key" ON "prisma"."FeatureUsage"("userId", "feature", "action", "date");

-- CreateIndex
CREATE INDEX "FeatureMetrics_feature_idx" ON "prisma"."FeatureMetrics"("feature");

-- CreateIndex
CREATE INDEX "FeatureMetrics_metric_idx" ON "prisma"."FeatureMetrics"("metric");

-- CreateIndex
CREATE INDEX "FeatureMetrics_timestamp_idx" ON "prisma"."FeatureMetrics"("timestamp");

-- CreateIndex
CREATE INDEX "FeatureAdoption_feature_idx" ON "prisma"."FeatureAdoption"("feature");

-- CreateIndex
CREATE INDEX "FeatureAdoption_adoptedAt_idx" ON "prisma"."FeatureAdoption"("adoptedAt");

-- CreateIndex
CREATE UNIQUE INDEX "FeatureAdoption_userId_feature_key" ON "prisma"."FeatureAdoption"("userId", "feature");

-- CreateIndex
CREATE INDEX "UserEvent_userId_idx" ON "prisma"."UserEvent"("userId");

-- CreateIndex
CREATE INDEX "UserEvent_event_idx" ON "prisma"."UserEvent"("event");

-- CreateIndex
CREATE INDEX "UserEvent_timestamp_idx" ON "prisma"."UserEvent"("timestamp");

-- CreateIndex
CREATE INDEX "Dashboard_ownerId_idx" ON "prisma"."Dashboard"("ownerId");

-- CreateIndex
CREATE INDEX "Dashboard_name_idx" ON "prisma"."Dashboard"("name");

-- CreateIndex
CREATE INDEX "DashboardWidget_dashboardId_idx" ON "prisma"."DashboardWidget"("dashboardId");

-- CreateIndex
CREATE INDEX "DashboardWidget_type_idx" ON "prisma"."DashboardWidget"("type");

-- CreateIndex
CREATE INDEX "NotificationChannel_type_idx" ON "prisma"."NotificationChannel"("type");

-- CreateIndex
CREATE INDEX "NotificationChannel_isEnabled_idx" ON "prisma"."NotificationChannel"("isEnabled");

-- CreateIndex
CREATE INDEX "AuditLog_action_timestamp_idx" ON "prisma"."AuditLog"("action", "timestamp");

-- CreateIndex
CREATE INDEX "AuditLog_severity_idx" ON "prisma"."AuditLog"("severity");

-- CreateIndex
CREATE INDEX "AuthAttempt_type_idx" ON "prisma"."AuthAttempt"("type");

-- CreateIndex
CREATE INDEX "AuthAttempt_result_idx" ON "prisma"."AuthAttempt"("result");

-- CreateIndex
CREATE INDEX "AuthAttempt_ipAddress_idx" ON "prisma"."AuthAttempt"("ipAddress");

-- CreateIndex
CREATE INDEX "AuthAttempt_createdAt_idx" ON "prisma"."AuthAttempt"("createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "FeatureFlag_feature_key" ON "prisma"."FeatureFlag"("feature");

-- CreateIndex
CREATE INDEX "FeatureFlag_feature_idx" ON "prisma"."FeatureFlag"("feature");

-- CreateIndex
CREATE INDEX "FeatureFlag_enabled_idx" ON "prisma"."FeatureFlag"("enabled");

-- CreateIndex
CREATE INDEX "Permission_resource_idx" ON "prisma"."Permission"("resource");

-- CreateIndex
CREATE INDEX "Permission_action_idx" ON "prisma"."Permission"("action");

-- CreateIndex
CREATE INDEX "RefreshToken_expiresAt_idx" ON "prisma"."RefreshToken"("expiresAt");

-- CreateIndex
CREATE INDEX "Report_type_idx" ON "prisma"."Report"("type");

-- CreateIndex
CREATE INDEX "Report_status_idx" ON "prisma"."Report"("status");

-- CreateIndex
CREATE INDEX "Report_createdBy_idx" ON "prisma"."Report"("createdBy");

-- CreateIndex
CREATE INDEX "Report_createdAt_idx" ON "prisma"."Report"("createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "UserSession_sessionToken_key" ON "prisma"."UserSession"("sessionToken");

-- CreateIndex
CREATE INDEX "UserSession_sessionToken_idx" ON "prisma"."UserSession"("sessionToken");

-- CreateIndex
CREATE INDEX "UserSession_expiresAt_idx" ON "prisma"."UserSession"("expiresAt");

-- AddForeignKey
ALTER TABLE "public"."UserDepartment" ADD CONSTRAINT "UserDepartment_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."UserDepartment" ADD CONSTRAINT "UserDepartment_departmentId_fkey" FOREIGN KEY ("departmentId") REFERENCES "public"."Department"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."UserRole" ADD CONSTRAINT "UserRole_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."UserRole" ADD CONSTRAINT "UserRole_roleId_fkey" FOREIGN KEY ("roleId") REFERENCES "public"."Role"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Skill" ADD CONSTRAINT "Skill_departmentId_fkey" FOREIGN KEY ("departmentId") REFERENCES "public"."Department"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."UserSkill" ADD CONSTRAINT "UserSkill_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."UserSkill" ADD CONSTRAINT "UserSkill_skillId_fkey" FOREIGN KEY ("skillId") REFERENCES "public"."Skill"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Training" ADD CONSTRAINT "Training_departmentId_fkey" FOREIGN KEY ("departmentId") REFERENCES "public"."Department"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."TrainingSkill" ADD CONSTRAINT "TrainingSkill_trainingId_fkey" FOREIGN KEY ("trainingId") REFERENCES "public"."Training"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."TrainingSkill" ADD CONSTRAINT "TrainingSkill_skillId_fkey" FOREIGN KEY ("skillId") REFERENCES "public"."Skill"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."TrainingUser" ADD CONSTRAINT "TrainingUser_trainingId_fkey" FOREIGN KEY ("trainingId") REFERENCES "public"."Training"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."TrainingUser" ADD CONSTRAINT "TrainingUser_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."TrainingMaterial" ADD CONSTRAINT "TrainingMaterial_trainingId_fkey" FOREIGN KEY ("trainingId") REFERENCES "public"."Training"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."VendorCategory" ADD CONSTRAINT "VendorCategory_vendorId_fkey" FOREIGN KEY ("vendorId") REFERENCES "public"."Vendor"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."VendorContract" ADD CONSTRAINT "VendorContract_vendorId_fkey" FOREIGN KEY ("vendorId") REFERENCES "public"."Vendor"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Proposal" ADD CONSTRAINT "Proposal_vendorId_fkey" FOREIGN KEY ("vendorId") REFERENCES "public"."Vendor"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Review" ADD CONSTRAINT "Review_vendorId_fkey" FOREIGN KEY ("vendorId") REFERENCES "public"."Vendor"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."WeeklySubmission" ADD CONSTRAINT "WeeklySubmission_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."AiConversation" ADD CONSTRAINT "AiConversation_systemPromptId_fkey" FOREIGN KEY ("systemPromptId") REFERENCES "prisma"."PromptTemplate"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."AiMessage" ADD CONSTRAINT "AiMessage_conversationId_fkey" FOREIGN KEY ("conversationId") REFERENCES "prisma"."AiConversation"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."AiMemory" ADD CONSTRAINT "AiMemory_conversationId_fkey" FOREIGN KEY ("conversationId") REFERENCES "prisma"."AiConversation"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."Document" ADD CONSTRAINT "Document_folderId_fkey" FOREIGN KEY ("folderId") REFERENCES "prisma"."Folder"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."DocumentChunk" ADD CONSTRAINT "DocumentChunk_documentId_fkey" FOREIGN KEY ("documentId") REFERENCES "prisma"."Document"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."Folder" ADD CONSTRAINT "Folder_parentId_fkey" FOREIGN KEY ("parentId") REFERENCES "prisma"."Folder"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."ErrorEvent" ADD CONSTRAINT "ErrorEvent_groupId_fkey" FOREIGN KEY ("groupId") REFERENCES "prisma"."ErrorGroup"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."ErrorResolution" ADD CONSTRAINT "ErrorResolution_groupId_fkey" FOREIGN KEY ("groupId") REFERENCES "prisma"."ErrorGroup"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."Alert" ADD CONSTRAINT "Alert_ruleId_fkey" FOREIGN KEY ("ruleId") REFERENCES "prisma"."AlertRule"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."EmailSyncLog" ADD CONSTRAINT "EmailSyncLog_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "prisma"."EmailAccount"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."File" ADD CONSTRAINT "File_ownerId_fkey" FOREIGN KEY ("ownerId") REFERENCES "public"."User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."File" ADD CONSTRAINT "File_uploadedBy_fkey" FOREIGN KEY ("uploadedBy") REFERENCES "public"."User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."FileVersion" ADD CONSTRAINT "FileVersion_fileId_fkey" FOREIGN KEY ("fileId") REFERENCES "prisma"."File"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."DashboardWidget" ADD CONSTRAINT "DashboardWidget_dashboardId_fkey" FOREIGN KEY ("dashboardId") REFERENCES "prisma"."Dashboard"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."RefreshToken" ADD CONSTRAINT "RefreshToken_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."EmailVerificationToken" ADD CONSTRAINT "EmailVerificationToken_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."PasswordResetToken" ADD CONSTRAINT "PasswordResetToken_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
